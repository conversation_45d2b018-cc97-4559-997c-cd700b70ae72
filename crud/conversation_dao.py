import logging
from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy import func, and_, desc, asc
from typing import List, Optional, Tuple, Dict, Any
from datetime import datetime, timedelta

from models.conversation import Conversation
from models.message import Message
from utils.exceptions import DatabaseError, ConversationNotFoundError

logger = logging.getLogger(__name__)


class ConversationDao:
    """会话数据访问对象，提供优化的数据库查询"""

    @staticmethod
    def get_by_username(
        db: Session,
        username: str,
        page: int = 1,
        size: int = 20,
        include_message_count: bool = False
    ) -> Tuple[List[Conversation], int]:
        """
        分页获取用户的会话列表

        Args:
            db: 数据库会话
            username: 用户名
            page: 页码（从1开始）
            size: 每页大小
            include_message_count: 是否包含消息数量

        Returns:
            Tuple[List[Conversation], int]: (会话列表, 总数)
        """
        try:
            query = db.query(Conversation).filter(Conversation.username == username)

            # 如果需要消息数量，使用左连接
            if include_message_count:
                query = query.outerjoin(Message).group_by(Conversation.id)

            # 获取总数
            total = query.count()

            # 分页查询
            conversations = (
                query.order_by(desc(Conversation.created_at))
                .offset((page - 1) * size)
                .limit(size)
                .all()
            )

            logger.debug(f"Retrieved {len(conversations)} conversations for user {username}")
            return conversations, total

        except Exception as e:
            logger.error(f"Failed to get conversations for user {username}: {e}")
            raise DatabaseError(f"获取会话列表失败: {str(e)}")

    @staticmethod
    def get_by_id(
        db: Session,
        conversation_id: int,
        username: str = None,
        include_messages: bool = False
    ) -> Optional[Conversation]:
        """
        根据ID获取会话

        Args:
            db: 数据库会话
            conversation_id: 会话ID
            username: 用户名（用于权限验证）
            include_messages: 是否预加载消息

        Returns:
            Optional[Conversation]: 会话对象或None
        """
        try:
            query = db.query(Conversation).filter(Conversation.id == conversation_id)

            if username:
                query = query.filter(Conversation.username == username)

            # 如果需要消息，使用预加载
            if include_messages:
                query = query.options(
                    selectinload(Conversation.messages).options(
                        # 按时间戳排序消息
                        selectinload(Message).order_by(Message.timestamp)
                    )
                )

            conversation = query.first()

            if conversation:
                logger.debug(f"Retrieved conversation {conversation_id}")
            else:
                logger.warning(f"Conversation {conversation_id} not found")

            return conversation

        except Exception as e:
            logger.error(f"Failed to get conversation {conversation_id}: {e}")
            raise DatabaseError(f"获取会话失败: {str(e)}")

    @staticmethod
    def create(
        db: Session,
        conversation_id: int = None,
        username: str = None,
        title: str = "新的对话",
        auto_commit: bool = True
    ) -> Conversation:
        """
        创建新会话

        Args:
            db: 数据库会话
            conversation_id: 会话ID（可选）
            username: 用户名
            title: 会话标题
            auto_commit: 是否自动提交

        Returns:
            Conversation: 创建的会话对象
        """
        try:
            if conversation_id:
                conversation = Conversation(
                    id=conversation_id,
                    username=username,
                    title=title,
                    created_at=datetime.utcnow()
                )
            else:
                conversation = Conversation(
                    username=username,
                    title=title,
                    created_at=datetime.utcnow()
                )

            db.add(conversation)

            if auto_commit:
                db.commit()
                db.refresh(conversation)

            logger.info(f"Created conversation {conversation.id} for user {username}")
            return conversation

        except Exception as e:
            if auto_commit:
                db.rollback()
            logger.error(f"Failed to create conversation for user {username}: {e}")
            raise DatabaseError(f"创建会话失败: {str(e)}")

    @staticmethod
    def delete(
        db: Session,
        conversation: Conversation = None,
        conversation_id: int = None,
        username: str = None,
        auto_commit: bool = True
    ) -> bool:
        """
        删除会话及其所有消息

        Args:
            db: 数据库会话
            conversation: 会话对象
            conversation_id: 会话ID
            username: 用户名
            auto_commit: 是否自动提交

        Returns:
            bool: 删除成功返回True
        """
        try:
            if not conversation and conversation_id:
                conversation = ConversationDao.get_by_id(db, conversation_id, username)

            if not conversation:
                raise ConversationNotFoundError(conversation_id or 0)

            # 先删除相关消息
            db.query(Message).filter(Message.conversation_id == conversation.id).delete()

            # 再删除会话
            db.delete(conversation)

            if auto_commit:
                db.commit()

            logger.info(f"Deleted conversation {conversation.id}")
            return True

        except ConversationNotFoundError:
            raise
        except Exception as e:
            if auto_commit:
                db.rollback()
            logger.error(f"Failed to delete conversation: {e}")
            raise DatabaseError(f"删除会话失败: {str(e)}")
