"""Database connection and session management."""

import logging
from contextlib import asynccontextmanager
from typing import Generator, AsyncGenerator

from sqlalchemy import create_engine, event, pool
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool, StaticPool

from config.settings import settings
from utils.exceptions import DatabaseError

logger = logging.getLogger(__name__)

# Create Base class for models
Base = declarative_base()


def create_database_engine():
    """创建数据库引擎，支持连接池配置"""

    # 根据数据库类型配置连接参数
    connect_args = {}
    poolclass = QueuePool

    if settings.DATABASE_URL.startswith("sqlite"):
        # SQLite特殊配置
        connect_args = {
            "check_same_thread": False,
            "timeout": 20,
        }
        # SQLite使用StaticPool
        poolclass = StaticPool

        engine = create_engine(
            settings.DATABASE_URL,
            connect_args=connect_args,
            poolclass=poolclass,
            echo=settings.DEBUG,
            echo_pool=settings.DEBUG,
        )
    else:
        # PostgreSQL/MySQL等数据库配置
        engine = create_engine(
            settings.DATABASE_URL,
            pool_size=settings.DB_POOL_SIZE,
            max_overflow=settings.DB_MAX_OVERFLOW,
            pool_timeout=settings.DB_POOL_TIMEOUT,
            pool_recycle=settings.DB_POOL_RECYCLE,
            pool_pre_ping=True,  # 连接前检查连接是否有效
            echo=settings.DEBUG,
            echo_pool=settings.DEBUG,
        )

    # 添加连接事件监听器
    @event.listens_for(engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        """SQLite优化配置"""
        if settings.DATABASE_URL.startswith("sqlite"):
            cursor = dbapi_connection.cursor()
            # 启用外键约束
            cursor.execute("PRAGMA foreign_keys=ON")
            # 设置WAL模式提高并发性能
            cursor.execute("PRAGMA journal_mode=WAL")
            # 设置同步模式
            cursor.execute("PRAGMA synchronous=NORMAL")
            # 设置缓存大小
            cursor.execute("PRAGMA cache_size=10000")
            cursor.close()

    @event.listens_for(engine, "checkout")
    def receive_checkout(dbapi_connection, connection_record, connection_proxy):
        """连接检出时的日志"""
        logger.debug("Connection checked out from pool")

    @event.listens_for(engine, "checkin")
    def receive_checkin(dbapi_connection, connection_record):
        """连接归还时的日志"""
        logger.debug("Connection checked in to pool")

    return engine


# Create SQLAlchemy engine
engine = create_database_engine()

# Create SessionLocal class
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False  # 防止在事务提交后访问对象属性时出错
)


def get_database_session() -> Generator[Session, None, None]:
    """
    Get database session dependency.

    Yields:
        Session: SQLAlchemy database session
    """
    database_session = SessionLocal()
    try:
        yield database_session
    except Exception as e:
        database_session.rollback()
        logger.error(f"Database session error: {str(e)}")
        raise DatabaseError(f"数据库操作失败: {str(e)}")
    finally:
        database_session.close()


@asynccontextmanager
async def get_async_database_session() -> AsyncGenerator[Session, None]:
    """
    异步数据库会话上下文管理器

    Yields:
        Session: SQLAlchemy database session
    """
    database_session = SessionLocal()
    try:
        yield database_session
        database_session.commit()
    except Exception as e:
        database_session.rollback()
        logger.error(f"Async database session error: {str(e)}")
        raise DatabaseError(f"数据库操作失败: {str(e)}")
    finally:
        database_session.close()


def create_tables() -> None:
    """Create all database tables."""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {str(e)}")
        raise DatabaseError(f"创建数据库表失败: {str(e)}")


def check_database_health() -> bool:
    """检查数据库连接健康状态"""
    try:
        with engine.connect() as connection:
            connection.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        return False


def get_database_info() -> dict:
    """获取数据库连接信息"""
    pool = engine.pool
    return {
        "pool_size": getattr(pool, 'size', lambda: 0)(),
        "checked_in": getattr(pool, 'checkedin', lambda: 0)(),
        "checked_out": getattr(pool, 'checkedout', lambda: 0)(),
        "overflow": getattr(pool, 'overflow', lambda: 0)(),
        "invalid": getattr(pool, 'invalid', lambda: 0)(),
    }
