"""LDAP 认证服务实现"""

import logging
from typing import Optional, Dict, Any
from contextlib import contextmanager

from ldap3 import Server, Connection, ALL, SIMPLE, SYNC, core
from ldap3.core.exceptions import (
    LDAPException,
    LDAPBindError,
    LDAPInvalidCredentialsError,
    LDAPSocketOpenError,
    LDAPSocketReceiveError
)

from utils.exceptions import LDAPConnectionError, LDAPAuthenticationError

logger = logging.getLogger(__name__)


class LDAPService:
    """LDAP 认证服务"""

    def __init__(self, ldap_uri: str, base_dn: str, timeout: int = 3):
        self.ldap_uri = ldap_uri
        self.base_dn = base_dn
        self.timeout = timeout
        self.server = None
        self._initialize_server()

    def _initialize_server(self):
        """初始化LDAP服务器连接"""
        try:
            self.server = Server(
                self.ldap_uri,
                get_info=ALL,
                connect_timeout=self.timeout,
                mode=SYNC
            )
            logger.info(f"LDAP server initialized: {self.ldap_uri}")
        except Exception as e:
            logger.error(f"Failed to initialize LDAP server: {e}")
            raise LDAPConnectionError(f"LDAP服务器初始化失败: {str(e)}")

    @contextmanager
    def _get_connection(self, user_dn: str, password: str):
        """获取LDAP连接的上下文管理器"""
        connection = None
        try:
            connection = Connection(
                self.server,
                user=user_dn,
                password=password,
                authentication=SIMPLE,
                auto_bind=True,
                raise_exceptions=True
            )
            yield connection
        except LDAPInvalidCredentialsError as e:
            logger.warning(f"Invalid credentials for {user_dn}: {e}")
            raise LDAPAuthenticationError("用户名或密码错误")
        except LDAPBindError as e:
            logger.error(f"LDAP bind error for {user_dn}: {e}")
            raise LDAPAuthenticationError(f"LDAP绑定失败: {str(e)}")
        except (LDAPSocketOpenError, LDAPSocketReceiveError) as e:
            logger.error(f"LDAP connection error: {e}")
            raise LDAPConnectionError(f"LDAP连接失败: {str(e)}")
        except LDAPException as e:
            logger.error(f"LDAP exception for {user_dn}: {e}")
            raise LDAPConnectionError(f"LDAP操作失败: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected LDAP error for {user_dn}: {e}")
            raise LDAPConnectionError(f"LDAP服务异常: {str(e)}")
        finally:
            if connection:
                try:
                    connection.unbind()
                except Exception as e:
                    logger.warning(f"Failed to unbind LDAP connection: {e}")

    def authenticate_user(self, username: str, password: str) -> bool:
        """
        LDAP 用户认证

        Args:
            username: 用户名
            password: 密码

        Returns:
            bool: 认证成功返回 True，否则返回 False

        Raises:
            LDAPConnectionError: LDAP连接失败
            LDAPAuthenticationError: LDAP认证失败
        """
        if not username or not password:
            raise LDAPAuthenticationError("用户名和密码不能为空")

        # 构建用户DN
        user_dn = self._build_user_dn(username)
        logger.info(f"尝试 LDAP 认证: {user_dn}")

        try:
            with self._get_connection(user_dn, password) as conn:
                # 认证成功，可以进行额外的用户信息查询
                user_info = self._get_user_info(conn, user_dn)
                logger.info(f"LDAP认证成功: {username}")

                # 可以在这里添加用户信息缓存逻辑
                if user_info:
                    logger.debug(f"User info retrieved: {user_info.get('displayName', username)}")

                return True

        except (LDAPConnectionError, LDAPAuthenticationError):
            # 重新抛出已知异常
            raise
        except Exception as e:
            logger.error(f"Unexpected error during authentication for {username}: {e}")
            raise LDAPConnectionError(f"认证过程中发生未知错误: {str(e)}")

    def _build_user_dn(self, username: str) -> str:
        """构建用户DN"""
        # 支持多种DN格式
        if '@' in username:
            # 如果用户名包含@，可能是UPN格式
            return username
        else:
            # 标准CN格式
            return f"cn={username},{self.base_dn}"

    def _get_user_info(self, connection: Connection, user_dn: str) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        try:
            # 搜索用户信息
            search_filter = f"(distinguishedName={user_dn})"
            attributes = ['displayName', 'mail', 'department', 'title', 'telephoneNumber']

            if connection.search(
                search_base=self.base_dn,
                search_filter=search_filter,
                attributes=attributes
            ):
                if connection.entries:
                    entry = connection.entries[0]
                    user_info = {}
                    for attr in attributes:
                        value = getattr(entry, attr, None)
                        if value:
                            user_info[attr] = str(value)
                    return user_info

            return None

        except Exception as e:
            logger.warning(f"Failed to get user info for {user_dn}: {e}")
            return None

    def test_connection(self) -> bool:
        """测试LDAP连接"""
        try:
            # 尝试匿名连接测试服务器可达性
            test_conn = Connection(self.server, auto_bind=True)
            test_conn.unbind()
            logger.info("LDAP connection test successful")
            return True
        except Exception as e:
            logger.error(f"LDAP connection test failed: {e}")
            return False

    def search_users(self, search_term: str, max_results: int = 10) -> list:
        """搜索用户（需要管理员权限）"""
        # 这个方法需要管理员凭据，暂时不实现
        # 可以在需要时添加管理员连接逻辑
        logger.warning("User search not implemented - requires admin credentials")
        return []

    def get_server_info(self) -> Dict[str, Any]:
        """获取LDAP服务器信息"""
        try:
            if self.server and self.server.info:
                return {
                    "server_name": str(self.server.host),
                    "port": self.server.port,
                    "ssl": self.server.ssl,
                    "schema": str(self.server.info.schema) if self.server.info.schema else None,
                    "naming_contexts": list(self.server.info.naming_contexts) if self.server.info.naming_contexts else []
                }
            return {}
        except Exception as e:
            logger.error(f"Failed to get server info: {e}")
            return {"error": str(e)}
