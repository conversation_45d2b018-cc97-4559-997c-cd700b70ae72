"""异步LLM服务实现"""

import asyncio
import logging
import traceback
from datetime import datetime
from typing import List, AsyncGenerator, Dict, Optional
from functools import lru_cache

from langchain_core.documents import Document
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.runnables import RunnablePassthrough
from langchain_openai import ChatOpenAI

from config.settings import settings
from rag.rag_retriever import RAGRetriever
from services.cache_service import cache_service, get_cached_rag_result, cache_rag_result
from utils.exceptions import LLMServiceError, RAGServiceError

logger = logging.getLogger(__name__)


class AsyncLLMService:
    """异步LLM服务，负责RAG问答链路"""

    def __init__(self):
        """初始化异步LLM服务"""
        self.llm = self._create_llm()
        self._semaphore = asyncio.Semaphore(10)  # 限制并发数

    def _create_llm(self) -> ChatOpenAI:
        """创建LLM实例"""
        try:
            return ChatOpenAI(
                model=settings.MODEL_CHAT,
                api_key=settings.API_KEY,
                base_url=settings.API_URL,
                temperature=0.7,
                streaming=True,
                max_tokens=2000,
                timeout=30,  # 设置超时时间
                max_retries=3,  # 设置重试次数
            )
        except Exception as e:
            logger.error(f"Failed to create LLM instance: {e}")
            raise LLMServiceError(f"LLM实例创建失败: {str(e)}")

    async def _create_rag_chain(self, collection_name: str, input_params: Dict):
        """创建RAG处理链"""
        try:
            # 构建系统提示模板
            system_prompt = """你是一个专业的智能助手，请根据以下参考资料回答用户的问题。

参考资料：
{context}

请根据上述参考资料回答用户问题。回答时请注意：
1. 优先使用参考资料中的信息
2. 如果参考资料不足以回答问题，请明确说明
3. 保持回答的准确性和完整性
4. 适当引用资料来源
5. 使用简洁明了的语言

用户问题：{question}

回答："""

            prompt_template = ChatPromptTemplate.from_template(system_prompt)
            retriever = RAGRetriever(collection_name, input_params)
            
            # 构建RAG链
            rag_chain = (
                {
                    "context": retriever | self._format_context,
                    "question": RunnablePassthrough(),
                }
                | prompt_template
                | self.llm
                | StrOutputParser()
            )

            return rag_chain
            
        except Exception as e:
            logger.error(f"Failed to create RAG chain: {e}")
            raise RAGServiceError(f"RAG链创建失败: {str(e)}")

    def _format_context(self, documents: List[Document]) -> str:
        """格式化上下文"""
        if not documents:
            return "暂无相关参考资料。"

        context_parts = []
        for i, doc in enumerate(documents, 1):
            context_parts.append(f"## 参考资料 {i}")
            context_parts.append(doc.page_content)
            context_parts.append("")  # 空行分隔

        return "\n".join(context_parts)

    async def generate_chat_response_async(
        self, 
        message: str, 
        collection_name: str, 
        input_params: Dict = None,
        use_cache: bool = True
    ) -> AsyncGenerator[str, None]:
        """
        异步生成流式聊天回复

        Args:
            message: 用户消息
            collection_name: 知识库集合名称
            input_params: 输入参数
            use_cache: 是否使用缓存

        Yields:
            str: 流式回复内容片段
        """
        async with self._semaphore:  # 限制并发
            try:
                logger.info(f"开始异步生成回复: {message[:50]}...")
                
                # 检查缓存
                if use_cache:
                    cached_result = get_cached_rag_result(message, collection_name)
                    if cached_result:
                        logger.info("使用缓存的RAG结果")
                        # 模拟流式返回缓存结果
                        words = cached_result.split()
                        for word in words:
                            yield word + " "
                            await asyncio.sleep(0.01)  # 模拟流式延迟
                        return

                # 创建RAG链
                rag_chain = await self._create_rag_chain(collection_name, input_params or {})

                # 异步执行RAG链并流式返回
                full_response = ""
                async for chunk in self._async_stream_chain(rag_chain, message):
                    if chunk:
                        full_response += chunk
                        yield chunk

                # 缓存完整回复
                if use_cache and full_response:
                    cache_rag_result(message, collection_name, full_response)
                    logger.info(f"RAG结果已缓存，长度: {len(full_response)}")

            except asyncio.TimeoutError:
                logger.error("RAG生成超时")
                yield "抱歉，回复生成超时，请稍后重试。"
            except (LLMServiceError, RAGServiceError) as e:
                logger.error(f"RAG服务错误: {e}")
                yield f"抱歉，RAG服务暂时不可用: {str(e)}"
            except Exception as e:
                logger.error(f"异步生成回复失败: {traceback.format_exc()}")
                # 提供备用回复
                error_message = f"抱歉，服务暂时不可用。您的消息：'{message}'。当前时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                words = error_message.split()
                for word in words:
                    yield word + " "
                    await asyncio.sleep(0.1)

    async def _async_stream_chain(self, rag_chain, message: str) -> AsyncGenerator[str, None]:
        """异步流式执行RAG链"""
        try:
            # 使用asyncio.to_thread在线程池中执行同步代码
            loop = asyncio.get_event_loop()
            
            # 创建一个队列来传递流式数据
            queue = asyncio.Queue()
            
            def stream_to_queue():
                try:
                    for chunk in rag_chain.stream(message):
                        loop.call_soon_threadsafe(queue.put_nowait, chunk)
                except Exception as e:
                    loop.call_soon_threadsafe(queue.put_nowait, StopIteration(e))
                finally:
                    loop.call_soon_threadsafe(queue.put_nowait, StopIteration())
            
            # 在线程池中执行流式生成
            task = loop.run_in_executor(None, stream_to_queue)
            
            # 从队列中获取流式数据
            while True:
                try:
                    chunk = await asyncio.wait_for(queue.get(), timeout=30)
                    if isinstance(chunk, StopIteration):
                        if chunk.args:
                            raise chunk.args[0]
                        break
                    yield chunk
                except asyncio.TimeoutError:
                    logger.error("Stream timeout")
                    break
            
            # 等待任务完成
            await task
            
        except Exception as e:
            logger.error(f"Async stream chain error: {e}")
            raise

    async def get_relevant_documents_async(
        self, 
        query: str, 
        collection_name: str,
        input_params: Dict = None
    ) -> List[Document]:
        """异步获取相关文档"""
        try:
            # 在线程池中执行同步的文档检索
            loop = asyncio.get_event_loop()
            retriever = RAGRetriever(collection_name, input_params or {})
            
            documents = await loop.run_in_executor(
                None, 
                retriever._get_relevant_documents, 
                query
            )
            
            logger.info(f"异步检索到 {len(documents)} 个相关文档")
            return documents
            
        except Exception as e:
            logger.error(f"异步文档检索失败: {e}")
            raise RAGServiceError(f"文档检索失败: {str(e)}")

    async def health_check(self) -> Dict[str, any]:
        """异步健康检查"""
        try:
            # 测试LLM连接
            test_prompt = "Hello"
            start_time = datetime.now()
            
            # 简单的连接测试
            response = await asyncio.wait_for(
                self._test_llm_connection(test_prompt),
                timeout=10
            )
            
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            
            return {
                "status": "healthy",
                "response_time": response_time,
                "llm_available": True,
                "timestamp": datetime.now().isoformat()
            }
            
        except asyncio.TimeoutError:
            return {
                "status": "unhealthy",
                "error": "LLM connection timeout",
                "llm_available": False,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "llm_available": False,
                "timestamp": datetime.now().isoformat()
            }

    async def _test_llm_connection(self, test_prompt: str) -> str:
        """测试LLM连接"""
        loop = asyncio.get_event_loop()
        
        def sync_test():
            try:
                response = self.llm.invoke(test_prompt)
                return response.content if hasattr(response, 'content') else str(response)
            except Exception as e:
                raise LLMServiceError(f"LLM连接测试失败: {str(e)}")
        
        return await loop.run_in_executor(None, sync_test)


# 创建全局异步LLM服务实例
@lru_cache(maxsize=1)
def get_async_llm_service() -> AsyncLLMService:
    """获取异步LLM服务实例（单例模式）"""
    return AsyncLLMService()


# 兼容性函数，用于现有代码的平滑迁移
async def generate_async_response(
    message: str, 
    collection_name: str, 
    input_params: Dict = None
) -> AsyncGenerator[str, None]:
    """生成异步响应的便捷函数"""
    service = get_async_llm_service()
    async for chunk in service.generate_chat_response_async(message, collection_name, input_params):
        yield chunk
