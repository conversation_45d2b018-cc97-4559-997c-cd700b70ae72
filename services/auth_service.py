"""认证服务实现"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

from jose import JWTError, jwt

from config.settings import settings
from services.ldap_service import LDAPService
from services.cache_service import cache_service
from utils.exceptions import (
    AuthenticationError,
    LDAPConnectionError,
    LDAPAuthenticationError,
    ConfigurationError
)

logger = logging.getLogger(__name__)


class AuthService:
    """认证服务，支持LDAP认证和JWT令牌管理"""

    def __init__(self):
        try:
            self.ldap_service = LDAPService(
                settings.LDAP_URI,
                settings.LDAP_BASE_DN,
                settings.LDAP_TIMEOUT
            )
        except Exception as e:
            logger.error(f"Failed to initialize LDAP service: {e}")
            raise ConfigurationError(f"LDAP服务初始化失败: {str(e)}")

    def authenticate_user(self, username: str, password: str) -> bool:
        """
        用户认证（LDAP验证 + 缓存优化）

        Args:
            username: 用户名
            password: 密码

        Returns:
            bool: 认证成功返回 True，否则返回 False

        Raises:
            AuthenticationError: 认证失败
            LDAPConnectionError: LDAP连接失败
        """
        if not username or not password:
            raise AuthenticationError("用户名和密码不能为空")

        # 检查用户名格式
        if not self._validate_username(username):
            raise AuthenticationError("用户名格式不正确")

        # 检查是否在黑名单中
        if self._is_user_blocked(username):
            raise AuthenticationError("用户已被锁定，请联系管理员")

        try:
            # 尝试LDAP认证
            is_authenticated = self.ldap_service.authenticate_user(username, password)

            if is_authenticated:
                # 认证成功，清除失败计数
                self._clear_failed_attempts(username)
                # 缓存认证结果（短时间）
                self._cache_auth_success(username)
                logger.info(f"User {username} authenticated successfully")
                return True
            else:
                # 认证失败，记录失败次数
                self._record_failed_attempt(username)
                raise LDAPAuthenticationError("用户名或密码错误")

        except LDAPConnectionError as e:
            logger.error(f"LDAP connection error for user {username}: {e}")
            raise
        except LDAPAuthenticationError as e:
            logger.warning(f"LDAP authentication failed for user {username}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected authentication error for user {username}: {e}")
            raise AuthenticationError(f"认证过程中发生错误: {str(e)}")

    def create_access_token(
        self,
        username: str,
        expires_delta: Optional[timedelta] = None,
        additional_claims: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        创建 JWT 访问令牌

        Args:
            username: 用户名
            expires_delta: 过期时间增量
            additional_claims: 额外的声明

        Returns:
            str: JWT令牌
        """
        if not username:
            raise ValueError("用户名不能为空")

        # 构建基础载荷
        to_encode = {
            "sub": username,
            "type": "access_token",
            "iat": datetime.utcnow()
        }

        # 添加额外声明
        if additional_claims:
            to_encode.update(additional_claims)

        # 设置过期时间
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )

        to_encode["exp"] = expire

        try:
            encoded_jwt = jwt.encode(
                to_encode,
                settings.SECRET_KEY,
                algorithm=settings.ALGORITHM
            )

            # 缓存令牌信息
            self._cache_token_info(username, encoded_jwt, expire)

            logger.info(f"Access token created for user {username}")
            return encoded_jwt

        except Exception as e:
            logger.error(f"Failed to create access token for user {username}: {e}")
            raise AuthenticationError(f"令牌创建失败: {str(e)}")

    def verify_token(self, token: str) -> Optional[str]:
        """
        验证 JWT 令牌并返回用户名

        Args:
            token: JWT令牌

        Returns:
            Optional[str]: 用户名，验证失败返回None
        """
        if not token:
            return None

        try:
            # 检查令牌是否在黑名单中
            if self._is_token_blacklisted(token):
                logger.warning("Attempted to use blacklisted token")
                return None

            payload = jwt.decode(
                token,
                settings.SECRET_KEY,
                algorithms=[settings.ALGORITHM]
            )

            username = payload.get("sub")
            token_type = payload.get("type")

            # 验证令牌类型
            if token_type != "access_token":
                logger.warning(f"Invalid token type: {token_type}")
                return None

            # 验证用户是否仍然有效
            if username and self._is_user_blocked(username):
                logger.warning(f"Token verification failed: user {username} is blocked")
                return None

            return username

        except jwt.ExpiredSignatureError:
            logger.debug("Token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid token: {e}")
            return None
        except Exception as e:
            logger.error(f"Token verification error: {e}")
            return None

    def revoke_token(self, token: str) -> bool:
        """
        撤销令牌（加入黑名单）

        Args:
            token: 要撤销的令牌

        Returns:
            bool: 撤销成功返回True
        """
        try:
            # 解析令牌获取过期时间
            payload = jwt.decode(
                token,
                settings.SECRET_KEY,
                algorithms=[settings.ALGORITHM],
                options={"verify_exp": False}  # 不验证过期时间
            )

            exp = payload.get("exp")
            if exp:
                # 计算剩余有效时间
                expire_time = datetime.fromtimestamp(exp)
                remaining_time = expire_time - datetime.utcnow()

                if remaining_time.total_seconds() > 0:
                    # 将令牌加入黑名单
                    cache_key = f"blacklist_token:{token}"
                    cache_service.set(cache_key, True, int(remaining_time.total_seconds()))
                    logger.info("Token revoked successfully")
                    return True

            return True  # 已过期的令牌不需要加入黑名单

        except Exception as e:
            logger.error(f"Failed to revoke token: {e}")
            return False

    def _validate_username(self, username: str) -> bool:
        """验证用户名格式"""
        if not username or len(username) < 2 or len(username) > 50:
            return False

        # 简单的用户名格式验证
        import re
        pattern = r'^[a-zA-Z0-9._-]+$'
        return bool(re.match(pattern, username))

    def _is_user_blocked(self, username: str) -> bool:
        """检查用户是否被锁定"""
        cache_key = f"blocked_user:{username}"
        return cache_service.exists(cache_key)

    def _record_failed_attempt(self, username: str):
        """记录认证失败次数"""
        cache_key = f"failed_attempts:{username}"
        attempts = cache_service.get(cache_key) or 0
        attempts += 1

        # 设置失败次数，5分钟过期
        cache_service.set(cache_key, attempts, 300)

        # 如果失败次数超过阈值，锁定用户
        if attempts >= 5:
            block_key = f"blocked_user:{username}"
            cache_service.set(block_key, True, 1800)  # 锁定30分钟
            logger.warning(f"User {username} blocked due to too many failed attempts")

    def _clear_failed_attempts(self, username: str):
        """清除失败次数记录"""
        cache_key = f"failed_attempts:{username}"
        cache_service.delete(cache_key)

    def _cache_auth_success(self, username: str):
        """缓存认证成功状态"""
        cache_key = f"auth_success:{username}"
        cache_service.set(cache_key, True, 300)  # 5分钟

    def _cache_token_info(self, username: str, token: str, expire_time: datetime):
        """缓存令牌信息"""
        cache_key = f"token_info:{username}"
        token_info = {
            "token": token,
            "expire_time": expire_time.isoformat(),
            "created_at": datetime.utcnow().isoformat()
        }
        remaining_seconds = (expire_time - datetime.utcnow()).total_seconds()
        if remaining_seconds > 0:
            cache_service.set(cache_key, token_info, int(remaining_seconds))

    def _is_token_blacklisted(self, token: str) -> bool:
        """检查令牌是否在黑名单中"""
        cache_key = f"blacklist_token:{token}"
        return cache_service.exists(cache_key)
