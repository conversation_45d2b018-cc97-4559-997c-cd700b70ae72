"""简化的认证服务实现"""

from datetime import datetime, timedelta
from typing import Optional

from jose import JWTError, jwt

from config.settings import settings
from services.ldap_service import LDAPService


class AuthService:
    """简化的认证服务，不涉及数据库操作"""

    def __init__(self):
        self.ldap_service = LDAPService(
            settings.LDAP_URI, settings.LDAP_BASE_DN, settings.LDAP_TIMEOUT
        )

    def authenticate_user(self, username: str, password: str) -> bool:
        """
        用户认证（仅 LDAP 验证）

        Args:
            username: 用户名
            password: 密码

        Returns:
            bool: 认证成功返回 True，否则返回 False
        """
        return self.ldap_service.authenticate_user(username, password)

    def create_access_token(
        self, username: str, expires_delta: Optional[timedelta] = None
    ) -> str:
        """创建 JWT 访问令牌"""
        to_encode = {"sub": username}
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )

        to_encode.update({"exp": expire, "iat": datetime.utcnow()})
        encoded_jwt = jwt.encode(
            to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
        )
        return encoded_jwt

    def verify_token(self, token: str) -> Optional[str]:
        """验证 JWT 令牌并返回用户名"""
        try:
            payload = jwt.decode(
                token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
            )
            return payload.get("sub")
        except JWTError:
            return None
