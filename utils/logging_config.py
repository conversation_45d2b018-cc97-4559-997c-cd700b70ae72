"""日志配置模块"""

import os
import sys
import logging
import logging.config
from datetime import datetime
from typing import Dict, Any
from pathlib import Path

from config.settings import settings


class ContextFilter(logging.Filter):
    """添加上下文信息的过滤器"""
    
    def filter(self, record):
        # 添加环境信息
        record.environment = settings.ENVIRONMENT
        record.app_name = settings.APP_NAME
        record.app_version = settings.APP_VERSION
        
        # 添加请求ID（如果存在）
        if hasattr(record, 'request_id'):
            record.request_id = getattr(record, 'request_id', 'N/A')
        else:
            record.request_id = 'N/A'
        
        return True


class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record):
        import json
        
        # 基础日志信息
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "environment": getattr(record, 'environment', 'unknown'),
            "app_name": getattr(record, 'app_name', 'unknown'),
            "app_version": getattr(record, 'app_version', 'unknown'),
            "request_id": getattr(record, 'request_id', 'N/A'),
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        # 添加用户信息（如果存在）
        if hasattr(record, 'username'):
            log_entry["username"] = record.username
        
        # 添加请求信息（如果存在）
        if hasattr(record, 'method'):
            log_entry["method"] = record.method
        if hasattr(record, 'path'):
            log_entry["path"] = record.path
        if hasattr(record, 'status_code'):
            log_entry["status_code"] = record.status_code
        if hasattr(record, 'response_time'):
            log_entry["response_time"] = record.response_time
        
        return json.dumps(log_entry, ensure_ascii=False)


def setup_logging() -> None:
    """设置日志配置"""
    
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 日志配置
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "standard": {
                "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "detailed": {
                "format": "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(funcName)s() - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "json": {
                "()": JSONFormatter,
            }
        },
        "filters": {
            "context_filter": {
                "()": ContextFilter,
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": "INFO",
                "formatter": "standard",
                "filters": ["context_filter"],
                "stream": sys.stdout
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "detailed",
                "filters": ["context_filter"],
                "filename": log_dir / "app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf-8"
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filters": ["context_filter"],
                "filename": log_dir / "error.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf-8"
            },
            "json_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filters": ["context_filter"],
                "filename": log_dir / "app.json.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf-8"
            }
        },
        "loggers": {
            # 应用日志
            "": {
                "level": "INFO",
                "handlers": ["console", "file", "json_file"],
                "propagate": False
            },
            # 错误日志
            "error": {
                "level": "ERROR",
                "handlers": ["console", "error_file"],
                "propagate": False
            },
            # FastAPI日志
            "fastapi": {
                "level": "INFO",
                "handlers": ["console", "file"],
                "propagate": False
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console", "file"],
                "propagate": False
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["file"],
                "propagate": False
            },
            # SQLAlchemy日志
            "sqlalchemy.engine": {
                "level": "WARNING",
                "handlers": ["file"],
                "propagate": False
            },
            "sqlalchemy.pool": {
                "level": "WARNING",
                "handlers": ["file"],
                "propagate": False
            },
            # 第三方库日志
            "httpx": {
                "level": "WARNING",
                "handlers": ["file"],
                "propagate": False
            },
            "urllib3": {
                "level": "WARNING",
                "handlers": ["file"],
                "propagate": False
            }
        }
    }
    
    # 开发环境调整
    if settings.DEBUG:
        logging_config["loggers"][""]["level"] = "DEBUG"
        logging_config["handlers"]["console"]["level"] = "DEBUG"
        logging_config["loggers"]["sqlalchemy.engine"]["level"] = "INFO"
    
    # 生产环境调整
    if settings.is_production:
        # 生产环境不输出到控制台
        logging_config["loggers"][""]["handlers"] = ["file", "json_file"]
        logging_config["loggers"]["error"]["handlers"] = ["error_file"]
    
    # 应用配置
    logging.config.dictConfig(logging_config)
    
    # 设置根日志级别
    root_logger = logging.getLogger()
    if settings.DEBUG:
        root_logger.setLevel(logging.DEBUG)
    else:
        root_logger.setLevel(logging.INFO)


def get_logger(name: str) -> logging.Logger:
    """获取日志记录器"""
    return logging.getLogger(name)


def log_request(logger: logging.Logger, method: str, path: str, status_code: int, response_time: float, username: str = None):
    """记录请求日志"""
    extra = {
        "method": method,
        "path": path,
        "status_code": status_code,
        "response_time": response_time,
    }
    
    if username:
        extra["username"] = username
    
    logger.info(
        f"{method} {path} - {status_code} - {response_time:.3f}s",
        extra=extra
    )


def log_error(logger: logging.Logger, error: Exception, context: Dict[str, Any] = None):
    """记录错误日志"""
    extra = {"extra_fields": context or {}}
    
    logger.error(
        f"Error occurred: {type(error).__name__}: {str(error)}",
        exc_info=True,
        extra=extra
    )


def log_business_event(logger: logging.Logger, event: str, details: Dict[str, Any] = None, username: str = None):
    """记录业务事件日志"""
    extra = {"extra_fields": details or {}}
    
    if username:
        extra["username"] = username
    
    logger.info(f"Business event: {event}", extra=extra)


# 在模块加载时设置日志
setup_logging()
