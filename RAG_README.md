# RAG问答系统使用说明

## 概述

本项目已完善自定义的retrievers，实现了完整的RAG（检索增强生成）问答链路，支持流式响应和多种检索策略。

## 核心组件

### 1. 自定义Retrievers

#### RAGRetriever
- **功能**: 增强版RAG检索器，支持多种检索策略
- **特性**: 
  - 混合检索（语义+关键词）
  - 父子文档结构支持
  - 可配置的检索参数
  - 智能重排序
  - 内容格式化

#### MilvusRetriever  
- **功能**: 专门针对Milvus向量数据库的检索器
- **特性**:
  - 直接集成`search_hybrid_parent_child`方法
  - 智能内容组合
  - 详细元数据提取

### 2. LLM服务

#### LLMService
- **功能**: 完整的RAG问答链路服务
- **特性**:
  - LangChain集成
  - 流式响应支持
  - 可配置的提示模板
  - 异步处理支持

## 架构设计

```mermaid
graph TB
    A[用户问题] --> B[RAGRetriever]
    B --> C[Milvus混合检索]
    C --> D[文档重排序]
    D --> E[内容格式化]
    E --> F[RAG Chain]
    F --> G[LLM生成]
    G --> H[流式返回]
    
    I[配置管理] --> B
    J[依赖注入] --> K[服务实例]
    K --> B
    K --> F
```

## 使用方法

### 1. 基本使用

```python
from services.llm_service import get_llm_service

# 获取LLM服务实例
llm_service = get_llm_service()

# 检索相关文档
documents = llm_service.get_relevant_documents("什么是人工智能？")

# 生成流式回复
for chunk in llm_service.generate_chat_response("解释机器学习"):
    print(chunk, end='')
```

### 2. 配置检索参数

```python
# 更新检索配置
config = {
    'k': 5,                    # 检索文档数量
    'semantic_weight': 0.7,    # 语义检索权重
    'min_score': 0.1,          # 最小相关度分数
    'sort_by': 'max_score',    # 排序方式
    'enable_rerank': True,     # 启用重排序
    'rerank_top_k': 3,         # 重排序后保留数量
    'max_content_length': 15000 # 最大内容长度
}

llm_service.update_retrieval_config(config)
```

### 3. API接口使用

#### 文档检索接口
```bash
POST /api/rag/retrieve
{
    "query": "什么是深度学习？",
    "k": 5,
    "semantic_weight": 0.7,
    "min_score": 0.1
}
```

#### 配置管理接口
```bash
# 获取当前配置
GET /api/rag/config

# 更新配置
POST /api/rag/config
{
    "k": 5,
    "semantic_weight": 0.8,
    "min_score": 0.15,
    "enable_rerank": true
}

# 健康检查
GET /api/rag/health
```

#### 聊天流式接口
```bash
POST /api/chat/stream
{
    "conversation_id": 1,
    "message": "请解释什么是自然语言处理？"
}
```

## 配置参数说明

### 检索配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `k` | int | 5 | 检索返回的文档数量 |
| `semantic_weight` | float | 0.7 | 语义检索权重（0-1） |
| `min_score` | float | 0.1 | 最小相关度分数阈值 |
| `sort_by` | str | "max_score" | 排序方式（max_score/avg_score/total_score/hit_count） |
| `enable_rerank` | bool | true | 是否启用重排序 |
| `rerank_top_k` | int | 3 | 重排序后保留的文档数量 |
| `max_content_length` | int | 15000 | 单个文档最大内容长度 |

### 环境配置

在`backend/config/settings.py`中配置：

```python
# RAG相关配置
MODEL_EMBEDDING = 'bge-m3'          # 嵌入模型
MODEL_CHAT = 'qwen3-32b'            # 聊天模型
MODEL_RERANK = 'bge-reranker-v2-m3' # 重排序模型
API_KEY = 'your-api-key'            # API密钥
API_URL = 'http://your-llm-url'     # LLM服务地址
API_RERANK_URL = 'http://rerank-url' # 重排序服务地址
MILVUS_URL = 'http://milvus-url'     # Milvus地址
MILVUS_USER = 'username'            # Milvus用户名
MILVUS_PASSWORD = 'password'        # Milvus密码
```

## 测试

### 运行测试脚本

```bash
cd backend
python test_rag.py
```

测试包括：
- Milvus检索器功能测试
- LLM服务功能测试  
- 检索配置测试
- RAG生成测试（需要LLM服务连接）

### 测试结果示例

```
==================================================
测试结果摘要:
==================================================
Milvus检索器: ✓ 通过
LLM服务: ✓ 通过
检索配置: ✓ 通过
RAG生成: ✓ 通过

总计: 4/4 个测试通过
🎉 所有测试通过！RAG功能正常
```

## 技术特性

### 1. 多级检索策略
- **语义检索**: 基于向量相似度的深度语义理解
- **关键词检索**: 基于BM25的精确关键词匹配
- **混合检索**: 加权融合语义和关键词检索结果
- **父子文档**: 支持层次化文档结构检索

### 2. 智能重排序
- 集成BGE-reranker-v2-m3重排序模型
- 基于查询-文档相关性的精确排序
- 可配置的重排序阈值和数量

### 3. 流式响应
- 支持LangChain流式处理
- 实时返回生成内容
- SSE协议实现前端实时展示

### 4. 依赖注入
- 单例模式管理服务实例
- FastAPI依赖注入集成
- 灵活的配置管理

## 集成方式

### 1. 替换现有MessageService

原有的mock回复已被替换为完整的RAG链路：

```python
# 原来
def generate_chat_response(self, message: str):
    mock_response = "这是一个模拟的AI回复"
    for char in mock_response:
        yield char

# 现在
def generate_chat_response(self, message: str):
    # 使用LLM服务生成RAG回复
    yield from self.llm_service.generate_chat_response(message)
```

### 2. 新增RAG API路由

- `/api/rag/retrieve` - 文档检索
- `/api/rag/config` - 配置管理
- `/api/rag/health` - 健康检查

### 3. 向后兼容

保留了原有的`MyCustomRetriever`类以确保向后兼容性。

## 扩展功能

### 1. 自定义检索策略

可以通过继承`BaseRetriever`实现自定义检索逻辑：

```python
class CustomRetriever(BaseRetriever):
    def _get_relevant_documents(self, query: str) -> List[Document]:
        # 实现自定义检索逻辑
        pass
```

### 2. 多Collection支持

通过配置不同的collection_name支持多知识库：

```python
milvus_service = get_milvus_service("technical_docs")
retriever = RAGRetriever(milvus_service)
```

### 3. 个性化配置

可为不同用户或场景配置不同的检索参数：

```python
user_config = get_user_rag_config(username)
llm_service.update_retrieval_config(user_config)
```

## 注意事项

1. **依赖服务**: 确保Milvus、LLM API服务正常运行
2. **内存管理**: 大文档检索时注意内存使用
3. **网络连接**: LLM API调用需要稳定的网络连接
4. **并发控制**: 生产环境建议配置适当的并发限制
5. **日志监控**: 建议配置详细的日志记录用于问题排查

## 故障排除

### 常见问题

1. **Milvus连接失败**
   - 检查MILVUS_URL配置
   - 确认Milvus服务状态
   - 验证用户名密码

2. **LLM API调用失败**
   - 检查API_KEY和API_URL配置
   - 确认网络连接
   - 查看API服务状态

3. **检索结果为空**
   - 降低min_score阈值
   - 检查集合中是否有数据
   - 验证查询词是否合适

4. **生成回复异常**
   - 检查提示模板格式
   - 确认LLM模型配置
   - 查看详细错误日志

通过以上完善的RAG系统，您现在拥有了一个功能完整、可配置、易扩展的智能问答系统。 