"""Conversations API routes."""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from crud.database import get_database_session
from schemas.conversation import ConversationCreate, ConversationResponse
from services.conversation_service import ConversationService
from utils.dependencies import get_current_username

router = APIRouter(prefix="/api", tags=["会话管理"])
conversation_service = ConversationService()


@router.get(
    "/conversations", response_model=List[ConversationResponse], summary="获取对话列表"
)
async def get_conversations(
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
):
    """获取当前用户的所有对话列表"""
    conversations = conversation_service.get_user_conversations(db, current_username)
    return conversations


@router.post(
    "/conversations/new", response_model=ConversationResponse, summary="新建对话"
)
async def create_conversation(
    conversation_data: ConversationCreate,
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
):
    """为当前用户创建新的对话"""
    conversation = conversation_service.create_conversation(
        db, current_username, conversation_data.title
    )
    return conversation


@router.delete("/conversations/{conversation_id}", summary="删除对话")
async def delete_conversation(
    conversation_id: int,
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
):
    """删除指定的对话"""
    success = conversation_service.delete_conversation(
        db, conversation_id, current_username
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="对话不存在或无权限访问"
        )

    return {"message": "对话删除成功"}
